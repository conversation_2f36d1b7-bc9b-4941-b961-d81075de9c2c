# WRANOVSKY Contact Management System

A pure PHP contact management system for the WRANOVSKY Bohemian Crystal Chandeliers website, featuring a secure admin dashboard for managing customer inquiries.

## Features

### Frontend Website
- **Responsive Design**: Mobile-friendly website with Bootstrap 5
- **Contact Form**: Secure contact form with file upload capability
- **Modern UI**: Clean, professional design matching the crystal chandelier theme
- **SEO Optimized**: Proper meta tags, structured data, and semantic HTML
- **Performance**: Optimized images, lazy loading, and efficient CSS/JS

### Admin Dashboard
- **Secure Authentication**: Login system with session management and rate limiting
- **Contact Management**: View, filter, and manage all contact form submissions
- **File Attachments**: Handle and download customer file uploads
- **Status Tracking**: Mark contacts as new, read, replied, or archived
- **Priority System**: Assign priority levels (urgent, high, normal, low)
- **Search & Filter**: Advanced filtering by status, priority, date, and keywords
- **Responsive Admin**: Mobile-friendly admin interface
- **Activity Logging**: Track all admin actions for audit purposes

### Security Features
- **CSRF Protection**: Cross-site request forgery protection
- **Input Validation**: Comprehensive server-side validation
- **File Upload Security**: Restricted file types and secure storage
- **Password Hashing**: Bcrypt password hashing
- **Session Security**: Secure session management with timeouts
- **SQL Injection Prevention**: Prepared statements throughout
- **XSS Protection**: Output escaping and content security

## Technology Stack

- **Backend**: Pure PHP 7.4+ (no frameworks)
- **Database**: MySQL 5.7+ with PDO
- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript
- **Security**: Built-in PHP security features + custom implementations
- **File Handling**: Secure file upload and storage system

## Quick Start

### 1. System Requirements
- PHP 7.4+ with PDO MySQL, mbstring, fileinfo extensions
- MySQL 5.7+ or MariaDB 10.2+
- Apache 2.4+ or Nginx 1.18+
- 128MB+ PHP memory limit

### 2. Installation
```bash
# 1. Clone or download the project files
# 2. Create MySQL database
mysql -u root -p -e "CREATE DATABASE wranovsky_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 3. Import database schema
mysql -u root -p wranovsky_cms < database_schema.sql

# 4. Configure database connection
cp config/database.php.example config/database.php
# Edit config/database.php with your database credentials

# 5. Set file permissions
chmod -R 755 uploads/
chmod 644 config/*.php
```

### 3. Configuration
Edit `config/config.php`:
```php
// Update these settings
define('SITE_URL', 'https://yourdomain.com');
define('COMPANY_EMAIL', '<EMAIL>');
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

### 4. Default Admin Access
- **URL**: `https://yourdomain.com/admin/login.php`
- **Username**: admin
- **Password**: admin123
- **⚠️ Change password immediately after first login!**

## Project Structure

```
wranovsky/
├── admin/                  # Admin dashboard
│   ├── login.php          # Admin login page
│   ├── dashboard.php      # Main dashboard
│   ├── contacts.php       # Contact management
│   └── logout.php         # Logout functionality
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Images and media
├── config/               # Configuration files
│   ├── config.php        # Main configuration
│   └── database.php      # Database configuration
├── includes/             # PHP includes
│   ├── header.php        # Website header template
│   ├── footer.php        # Website footer template
│   ├── functions.php     # Utility functions
│   └── security.php      # Security functions
├── uploads/              # File upload directory
│   └── contacts/         # Contact form attachments
├── index.php             # Homepage
├── contact.php           # Contact page with form
├── database_schema.sql   # Database setup script
├── INSTALLATION.md       # Detailed installation guide
├── DATABASE_DESIGN.md    # Database documentation
└── README.md            # This file
```

## Database Schema

### Core Tables
- **admin_users**: Administrator accounts
- **contact_submissions**: Contact form submissions
- **contact_attachments**: File attachments
- **admin_activity_log**: Admin action logging
- **email_templates**: Email templates
- **system_settings**: Configuration settings

See `DATABASE_DESIGN.md` for detailed schema documentation.

## Key Features Explained

### Contact Form Processing
- Validates all input data server-side
- Supports multiple file attachments
- Stores submissions in database
- Sends email notifications
- CSRF protection enabled

### Admin Dashboard
- Real-time statistics and metrics
- Advanced filtering and search
- Bulk actions for contact management
- Responsive design for mobile access
- Activity logging for audit trails

### Security Implementation
- Password hashing with bcrypt
- Session timeout and regeneration
- Rate limiting for login attempts
- File upload restrictions
- SQL injection prevention
- XSS protection throughout

## Customization

### Adding New Pages
1. Create new PHP file in root directory
2. Include header: `include 'includes/header.php';`
3. Add your content
4. Include footer: `include 'includes/footer.php';`

### Modifying Styles
- Edit `assets/css/style.css` for frontend styles
- Edit `assets/css/admin.css` for admin panel styles
- Use CSS custom properties for easy color changes

### Email Templates
- Modify templates in the database via admin panel
- Support for HTML and plain text versions
- Variable substitution ({{first_name}}, {{message}}, etc.)

## File Upload Configuration

### Allowed File Types
- Images: JPG, JPEG, PNG, GIF
- Documents: TXT, PDF, DOC, DOCX
- Maximum size: 5MB per file (configurable)

### Security Measures
- File type validation by extension and MIME type
- Unique filename generation to prevent conflicts
- Storage outside web root (recommended)
- Execution prevention in upload directory

## Email Configuration

### SMTP Settings
Configure in `config/config.php`:
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

### Email Features
- Automatic confirmation emails to customers
- Admin notification emails
- HTML and plain text support
- Template system with variables

## Performance Optimization

### Frontend
- Minified CSS and JavaScript
- Image optimization and lazy loading
- Browser caching headers
- Gzip compression support

### Backend
- Database query optimization
- Prepared statements for security and performance
- Session optimization
- File upload optimization

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Contributing

This is a custom project for WRANOVSKY. For modifications:
1. Test thoroughly in development environment
2. Backup database before changes
3. Follow existing code style and security practices
4. Document any new features or changes

## License

Custom proprietary software for WRANOVSKY. All rights reserved.

## Support

For technical support:
1. Check `INSTALLATION.md` for setup issues
2. Review `DATABASE_DESIGN.md` for database questions
3. Check PHP error logs for debugging
4. Ensure all system requirements are met

## Changelog

### Version 1.0.0 (Initial Release)
- Complete PHP conversion from Squarespace
- Admin dashboard with contact management
- Secure file upload system
- Email notification system
- Responsive design implementation
- Security features implementation
