<!DOCTYPE html>
<html lang="<?php echo get_current_language(); ?>" dir="<?php echo get_language_direction(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title><?php echo isset($page_title) ? escape_html($page_title) . ' - ' : ''; ?><?php echo escape_html(SITE_NAME); ?></title>
    
    <meta name="description" content="<?php echo isset($page_description) ? escape_html($page_description) : 'Discover the highest quality Bohemian crystal chandeliers. Proudly handmade in the Czech Republic, we manufacture traditional & modern chandeliers and custom lighting.'; ?>">
    <meta name="keywords" content="bohemian crystal, chandeliers, czech republic, handmade, lighting, crystal lighting, traditional chandeliers, modern chandeliers">
    <meta name="author" content="WRANOVSKY">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo escape_attr(current_url()); ?>">
    <meta property="og:title" content="<?php echo isset($page_title) ? escape_attr($page_title) . ' - ' : ''; ?><?php echo escape_attr(SITE_NAME); ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? escape_attr($page_description) : 'Discover the highest quality Bohemian crystal chandeliers. Proudly handmade in the Czech Republic.'; ?>">
    <meta property="og:image" content="<?php echo ASSETS_URL; ?>/images/logo.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo escape_attr(current_url()); ?>">
    <meta property="twitter:title" content="<?php echo isset($page_title) ? escape_attr($page_title) . ' - ' : ''; ?><?php echo escape_attr(SITE_NAME); ?>">
    <meta property="twitter:description" content="<?php echo isset($page_description) ? escape_attr($page_description) : 'Discover the highest quality Bohemian crystal chandeliers. Proudly handmade in the Czech Republic.'; ?>">
    <meta property="twitter:image" content="<?php echo ASSETS_URL; ?>/images/logo.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/style.css" rel="stylesheet">
    
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link href="<?php echo escape_attr($css); ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <img src="<?php echo ASSETS_URL; ?>/images/logo.png" alt="<?php echo escape_attr(COMPANY_NAME); ?>" height="40">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>"><?php echo t('nav_home'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'about.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/about.php"><?php echo t('nav_about'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'products.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/products.php"><?php echo t('nav_products'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'projects.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/projects.php"><?php echo t('nav_projects'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'news.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/news.php"><?php echo t('nav_news'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/contact.php"><?php echo t('nav_contact'); ?></a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo get_language_name(); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo get_language_url('en'); ?>">English</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_language_url('ar'); ?>">العربية</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <?php if (isset($show_hero) && $show_hero): ?>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-4"><?php echo isset($hero_title) ? escape_html($hero_title) : 'Bohemian Crystal Chandeliers'; ?></h1>
                        <p class="lead mb-4"><?php echo isset($hero_subtitle) ? escape_html($hero_subtitle) : 'Discover the highest quality Bohemian crystal chandeliers. Proudly handmade in the Czech Republic.'; ?></p>
                        <?php if (isset($hero_cta_text) && isset($hero_cta_link)): ?>
                        <a href="<?php echo escape_attr($hero_cta_link); ?>" class="btn btn-primary btn-lg"><?php echo escape_html($hero_cta_text); ?></a>
                        <?php endif; ?>
                    </div>
                    <div class="col-lg-6">
                        <img src="<?php echo isset($hero_image) ? escape_attr($hero_image) : ASSETS_URL . '/images/hero-chandelier.jpg'; ?>" alt="Bohemian Crystal Chandelier" class="img-fluid rounded shadow">
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>
        
        <?php if (isset($page_header) && $page_header): ?>
        <!-- Page Header -->
        <section class="page-header bg-light py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h1 class="h2 mb-3"><?php echo escape_html($page_title ?? 'Page Title'); ?></h1>
                        <?php if (isset($page_breadcrumb) && $page_breadcrumb): ?>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">Home</a></li>
                                <?php foreach ($page_breadcrumb as $item): ?>
                                    <?php if (isset($item['url'])): ?>
                                        <li class="breadcrumb-item"><a href="<?php echo escape_attr($item['url']); ?>"><?php echo escape_html($item['title']); ?></a></li>
                                    <?php else: ?>
                                        <li class="breadcrumb-item active" aria-current="page"><?php echo escape_html($item['title']); ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ol>
                        </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>
        
        <!-- Content Area -->
        <div class="content-area py-5">
            <div class="container">
                <?php if (isset($_SESSION['success_message'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo escape_html($_SESSION['success_message']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['error_message'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo escape_html($_SESSION['error_message']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>
