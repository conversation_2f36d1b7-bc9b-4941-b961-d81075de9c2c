<?php
/**
 * Multi-Language Support System
 * WRANOVSKY Contact Management System
 * Supports: English (en) and Arabic (ar)
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Default language
define('DEFAULT_LANGUAGE', 'en');
define('SUPPORTED_LANGUAGES', ['en', 'ar']);

/**
 * Get current language
 */
function get_current_language() {
    // Check URL parameter first
    if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES)) {
        $_SESSION['language'] = $_GET['lang'];
        return $_GET['lang'];
    }
    
    // Check session
    if (isset($_SESSION['language']) && in_array($_SESSION['language'], SUPPORTED_LANGUAGES)) {
        return $_SESSION['language'];
    }
    
    // Check browser language
    if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
        $browser_lang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);
        if (in_array($browser_lang, SUPPORTED_LANGUAGES)) {
            $_SESSION['language'] = $browser_lang;
            return $browser_lang;
        }
    }
    
    // Default to English
    $_SESSION['language'] = DEFAULT_LANGUAGE;
    return DEFAULT_LANGUAGE;
}

/**
 * Set current language
 */
function set_language($lang) {
    if (in_array($lang, SUPPORTED_LANGUAGES)) {
        $_SESSION['language'] = $lang;
        return true;
    }
    return false;
}

/**
 * Load language file
 */
function load_language($lang = null) {
    if ($lang === null) {
        $lang = get_current_language();
    }
    
    $lang_file = ROOT_PATH . "/languages/{$lang}.php";
    
    if (file_exists($lang_file)) {
        return include $lang_file;
    }
    
    // Fallback to English
    $fallback_file = ROOT_PATH . "/languages/en.php";
    if (file_exists($fallback_file)) {
        return include $fallback_file;
    }
    
    return [];
}

// Global language variables
$current_language = get_current_language();
$translations = load_language($current_language);

/**
 * Translate function
 */
function t($key, $default = null) {
    global $translations;
    
    if (isset($translations[$key])) {
        return $translations[$key];
    }
    
    return $default ?: $key;
}

/**
 * Translate with parameters
 */
function tf($key, $params = [], $default = null) {
    $text = t($key, $default);
    
    foreach ($params as $param => $value) {
        $text = str_replace('{' . $param . '}', $value, $text);
    }
    
    return $text;
}

/**
 * Get language direction (LTR/RTL)
 */
function get_language_direction($lang = null) {
    if ($lang === null) {
        $lang = get_current_language();
    }
    
    $rtl_languages = ['ar', 'he', 'fa', 'ur'];
    return in_array($lang, $rtl_languages) ? 'rtl' : 'ltr';
}

/**
 * Check if current language is RTL
 */
function is_rtl() {
    return get_language_direction() === 'rtl';
}

/**
 * Get language name
 */
function get_language_name($lang = null) {
    if ($lang === null) {
        $lang = get_current_language();
    }
    
    $names = [
        'en' => 'English',
        'ar' => 'العربية'
    ];
    
    return $names[$lang] ?? $lang;
}

/**
 * Get language URL
 */
function get_language_url($lang) {
    $current_url = $_SERVER['REQUEST_URI'];
    $parsed_url = parse_url($current_url);
    
    // Remove existing lang parameter
    if (isset($parsed_url['query'])) {
        parse_str($parsed_url['query'], $query_params);
        unset($query_params['lang']);
        $query_string = http_build_query($query_params);
    } else {
        $query_string = '';
    }
    
    // Add new lang parameter
    $new_params = $query_string ? $query_string . '&lang=' . $lang : 'lang=' . $lang;
    
    return $parsed_url['path'] . '?' . $new_params;
}

/**
 * Generate language switcher HTML
 */
function language_switcher($class = 'language-switcher') {
    $current_lang = get_current_language();
    $html = '<div class="' . $class . '">';
    
    foreach (SUPPORTED_LANGUAGES as $lang) {
        $active_class = ($lang === $current_lang) ? ' active' : '';
        $lang_name = get_language_name($lang);
        $lang_url = get_language_url($lang);
        
        $html .= '<a href="' . htmlspecialchars($lang_url) . '" class="lang-link' . $active_class . '" data-lang="' . $lang . '">';
        $html .= htmlspecialchars($lang_name);
        $html .= '</a>';
    }
    
    $html .= '</div>';
    return $html;
}

/**
 * Format date according to language
 */
function format_date_localized($date, $format = null) {
    $lang = get_current_language();
    
    if ($format === null) {
        $format = ($lang === 'ar') ? 'd/m/Y' : 'M j, Y';
    }
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    
    if ($lang === 'ar') {
        // Arabic date formatting
        setlocale(LC_TIME, 'ar_SA.UTF-8', 'ar_SA', 'arabic');
    } else {
        // English date formatting
        setlocale(LC_TIME, 'en_US.UTF-8', 'en_US', 'english');
    }
    
    return date($format, $timestamp);
}

/**
 * Format number according to language
 */
function format_number_localized($number) {
    $lang = get_current_language();
    
    if ($lang === 'ar') {
        // Arabic numerals
        $arabic_numerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $english_numerals = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        return str_replace($english_numerals, $arabic_numerals, $number);
    }
    
    return $number;
}

/**
 * Get text alignment class for current language
 */
function text_align_class() {
    return is_rtl() ? 'text-end' : 'text-start';
}

/**
 * Get float direction for current language
 */
function float_class($direction = 'start') {
    if (is_rtl()) {
        return $direction === 'start' ? 'float-end' : 'float-start';
    }
    return $direction === 'start' ? 'float-start' : 'float-end';
}

/**
 * Get margin/padding direction classes
 */
function margin_class($side, $size = '') {
    if (is_rtl()) {
        $side = str_replace(['start', 'end'], ['temp', 'start'], $side);
        $side = str_replace('temp', 'end', $side);
    }
    return 'm' . ($side ? $side : '') . ($size ? '-' . $size : '');
}

function padding_class($side, $size = '') {
    if (is_rtl()) {
        $side = str_replace(['start', 'end'], ['temp', 'start'], $side);
        $side = str_replace('temp', 'end', $side);
    }
    return 'p' . ($side ? $side : '') . ($size ? '-' . $size : '');
}
?>
