-- WRANOVSKY Contact Management System Database Schema
-- MySQL Database Design for Contact Form Management and Admin Dashboard

-- Create database
CREATE DATABASE IF NOT EXISTS wranovsky_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE wranovsky_cms;

-- Admin users table for dashboard authentication
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'manager') DEFAULT 'manager',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact form submissions table
CREATE TABLE contact_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    form_type ENUM('general', 'product_inquiry') DEFAULT 'general',
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NULL,
    subject VARCHAR(255) NULL,
    message TEXT NOT NULL,
    product_title VARCHAR(255) NULL, -- For product inquiries
    product_url VARCHAR(500) NULL,   -- For product inquiries
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    assigned_to INT NULL, -- Reference to admin_users.id
    replied_at TIMESTAMP NULL,
    replied_by INT NULL, -- Reference to admin_users.id
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_email (email),
    INDEX idx_form_type (form_type),
    FOREIGN KEY (assigned_to) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (replied_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- File attachments table for contact form uploads
CREATE TABLE contact_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contact_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_extension VARCHAR(10) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_contact_id (contact_id),
    FOREIGN KEY (contact_id) REFERENCES contact_submissions(id) ON DELETE CASCADE
);

-- Admin activity log for tracking actions
CREATE TABLE admin_activity_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50) NULL, -- 'contact', 'user', 'system'
    target_id INT NULL,
    description TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_admin_id (admin_id),
    INDEX idx_created_at (created_at),
    INDEX idx_action (action),
    FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- Email templates for automated responses
CREATE TABLE email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body_text TEXT NOT NULL,
    body_html TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES admin_users(id)
);

-- System settings table
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    updated_by INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Insert default admin user (password: admin123 - should be changed immediately)
INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin');

-- Insert default email templates
INSERT INTO email_templates (name, subject, body_text, body_html, created_by) VALUES 
('contact_confirmation', 'Thank you for contacting WRANOVSKY', 
'Dear {{first_name}} {{last_name}},

Thank you for contacting WRANOVSKY - Bohemian Crystal Chandeliers Manufacturer.

We have received your message and will respond within 24-48 hours.

Your inquiry details:
Subject: {{subject}}
Message: {{message}}

Best regards,
WRANOVSKY Team
Phone: +*********** 236
Email: <EMAIL>', 
'<p>Dear {{first_name}} {{last_name}},</p>
<p>Thank you for contacting <strong>WRANOVSKY - Bohemian Crystal Chandeliers Manufacturer</strong>.</p>
<p>We have received your message and will respond within 24-48 hours.</p>
<h3>Your inquiry details:</h3>
<p><strong>Subject:</strong> {{subject}}<br>
<strong>Message:</strong> {{message}}</p>
<p>Best regards,<br>
<strong>WRANOVSKY Team</strong><br>
Phone: +*********** 236<br>
Email: <EMAIL></p>', 1);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES 
('site_name', 'WRANOVSKY - Bohemian Crystal Chandeliers', 'string', 'Website name'),
('contact_email', '<EMAIL>', 'string', 'Main contact email'),
('contact_phone', '+*********** 236', 'string', 'Main contact phone'),
('company_address', 'Vesecko, Turnov, Liberecký kraj, 511 01, Czech Republic', 'string', 'Company address'),
('max_file_size', '5120', 'integer', 'Maximum file upload size in KB'),
('allowed_file_types', 'jpg,jpeg,png,gif,txt,pdf,doc,docx', 'string', 'Allowed file upload types'),
('auto_reply_enabled', '1', 'boolean', 'Enable automatic email replies'),
('items_per_page', '20', 'integer', 'Number of items per page in admin dashboard');

-- Create indexes for better performance
CREATE INDEX idx_contact_submissions_status_created ON contact_submissions(status, created_at);
CREATE INDEX idx_admin_activity_log_admin_created ON admin_activity_log(admin_id, created_at);
