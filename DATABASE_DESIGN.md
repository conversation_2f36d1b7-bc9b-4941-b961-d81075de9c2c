# WRANOVSKY Contact Management System - Database Design

## Overview
This database design supports a PHP-based contact management system for WRANOVSKY crystal chandeliers website, replacing the existing Squarespace-based contact forms with a custom admin dashboard.

## Database Schema

### Core Tables

#### 1. `admin_users`
Stores administrator accounts for dashboard access.

**Fields:**
- `id` (INT, PK, AUTO_INCREMENT) - Unique user identifier
- `username` (VARCHAR(50), UNIQUE) - Login username
- `email` (VARCHAR(100), UNIQUE) - Admin email address
- `password_hash` (VARCHAR(255)) - Bcrypt hashed password
- `full_name` (VARCHAR(100)) - Administrator's full name
- `role` (ENUM: 'admin', 'manager') - Permission level
- `is_active` (BOOLEAN) - Account status
- `last_login` (TIMESTAMP) - Last login time
- `created_at` (TIMESTAMP) - Account creation time
- `updated_at` (TIMESTAMP) - Last update time

#### 2. `contact_submissions`
Main table for storing contact form submissions.

**Fields:**
- `id` (INT, PK, AUTO_INCREMENT) - Unique submission identifier
- `form_type` (ENUM: 'general', 'product_inquiry') - Type of contact form
- `first_name` (VARCHAR(100)) - Contact's first name
- `last_name` (VARCHAR(100)) - Contact's last name
- `email` (VARCHAR(255)) - Contact's email address
- `phone` (VARCHAR(50)) - Optional phone number
- `subject` (VARCHAR(255)) - Message subject
- `message` (TEXT) - Main message content
- `product_title` (VARCHAR(255)) - For product inquiries
- `product_url` (VARCHAR(500)) - For product inquiries
- `ip_address` (VARCHAR(45)) - Submitter's IP address
- `user_agent` (TEXT) - Browser information
- `status` (ENUM: 'new', 'read', 'replied', 'archived') - Processing status
- `priority` (ENUM: 'low', 'normal', 'high', 'urgent') - Priority level
- `assigned_to` (INT, FK) - Assigned administrator
- `replied_at` (TIMESTAMP) - Reply timestamp
- `replied_by` (INT, FK) - Administrator who replied
- `created_at` (TIMESTAMP) - Submission time
- `updated_at` (TIMESTAMP) - Last update time

#### 3. `contact_attachments`
Stores file attachments from contact forms.

**Fields:**
- `id` (INT, PK, AUTO_INCREMENT) - Unique attachment identifier
- `contact_id` (INT, FK) - Reference to contact submission
- `original_filename` (VARCHAR(255)) - Original file name
- `stored_filename` (VARCHAR(255)) - Stored file name (for security)
- `file_path` (VARCHAR(500)) - Full file path
- `file_size` (INT) - File size in bytes
- `mime_type` (VARCHAR(100)) - File MIME type
- `file_extension` (VARCHAR(10)) - File extension
- `uploaded_at` (TIMESTAMP) - Upload time

#### 4. `admin_activity_log`
Tracks administrator actions for audit purposes.

**Fields:**
- `id` (INT, PK, AUTO_INCREMENT) - Unique log entry identifier
- `admin_id` (INT, FK) - Administrator who performed action
- `action` (VARCHAR(100)) - Action performed
- `target_type` (VARCHAR(50)) - Type of target (contact, user, system)
- `target_id` (INT) - ID of target object
- `description` (TEXT) - Detailed description
- `ip_address` (VARCHAR(45)) - Administrator's IP
- `user_agent` (TEXT) - Browser information
- `created_at` (TIMESTAMP) - Action timestamp

#### 5. `email_templates`
Stores email templates for automated responses.

**Fields:**
- `id` (INT, PK, AUTO_INCREMENT) - Unique template identifier
- `name` (VARCHAR(100), UNIQUE) - Template name
- `subject` (VARCHAR(255)) - Email subject template
- `body_text` (TEXT) - Plain text email body
- `body_html` (TEXT) - HTML email body
- `is_active` (BOOLEAN) - Template status
- `created_by` (INT, FK) - Creator administrator
- `created_at` (TIMESTAMP) - Creation time
- `updated_at` (TIMESTAMP) - Last update time

#### 6. `system_settings`
Stores configurable system settings.

**Fields:**
- `id` (INT, PK, AUTO_INCREMENT) - Unique setting identifier
- `setting_key` (VARCHAR(100), UNIQUE) - Setting name
- `setting_value` (TEXT) - Setting value
- `setting_type` (ENUM: 'string', 'integer', 'boolean', 'json') - Value type
- `description` (TEXT) - Setting description
- `updated_by` (INT, FK) - Last updater
- `updated_at` (TIMESTAMP) - Last update time

## Relationships

1. **admin_users → contact_submissions**: One-to-many (assigned_to, replied_by)
2. **contact_submissions → contact_attachments**: One-to-many
3. **admin_users → admin_activity_log**: One-to-many
4. **admin_users → email_templates**: One-to-many (created_by)
5. **admin_users → system_settings**: One-to-many (updated_by)

## Indexes

Performance indexes are created on frequently queried columns:
- `contact_submissions`: status, created_at, email, form_type
- `admin_activity_log`: admin_id, created_at, action
- `contact_attachments`: contact_id

## Default Data

### Default Admin User
- **Username**: admin
- **Email**: <EMAIL>
- **Password**: admin123 (should be changed immediately)
- **Role**: admin

### Default Email Template
- **Name**: contact_confirmation
- **Purpose**: Automatic reply to contact form submissions
- **Variables**: {{first_name}}, {{last_name}}, {{subject}}, {{message}}

### Default System Settings
- Site name, contact information, file upload limits, pagination settings

## Security Considerations

1. **Password Hashing**: Uses bcrypt with cost factor 10
2. **File Upload Security**: 
   - Stored filenames differ from original names
   - File type validation
   - Size limits
3. **SQL Injection Prevention**: Use prepared statements
4. **XSS Prevention**: Escape output data
5. **CSRF Protection**: Implement CSRF tokens
6. **Activity Logging**: Track all admin actions

## File Upload Configuration

- **Max Size**: 5MB (5120 KB)
- **Allowed Types**: jpg, jpeg, png, gif, txt, pdf, doc, docx
- **Storage**: Outside web root for security
- **Naming**: UUID-based filenames to prevent conflicts

## Usage Notes

1. **Contact Form Types**:
   - `general`: Standard contact form
   - `product_inquiry`: Product-specific inquiries

2. **Status Workflow**:
   - `new` → `read` → `replied` → `archived`

3. **Priority Levels**:
   - Used for sorting and highlighting urgent inquiries

4. **Email Templates**:
   - Support variable substitution
   - Both text and HTML versions
