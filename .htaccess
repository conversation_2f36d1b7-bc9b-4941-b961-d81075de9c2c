# WRANOVSKY Contact Management System - Apache Configuration

# Enable URL Rewriting
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Head<PERSON> always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
</IfModule>

# Hide sensitive files
<Files "*.sql">
    Require all denied
</Files>

<Files "*.md">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "*.txt">
    <RequireAll>
        Require all granted
        Require not env REDIRECT_STATUS
    </RequireAll>
</Files>

# Hide dot files (like .htaccess, .env, etc.)
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# Protect config directory
<Directory "config">
    Require all denied
</Directory>

# Protect includes directory from direct access
<Directory "includes">
    Require all denied
</Directory>

# Protect uploads directory from script execution
<Directory "uploads">
    # Prevent execution of scripts
    <Files "*.php">
        Require all denied
    </Files>
    <Files "*.phtml">
        Require all denied
    </Files>
    <Files "*.pl">
        Require all denied
    </Files>
    <Files "*.py">
        Require all denied
    </Files>
    <Files "*.jsp">
        Require all denied
    </Files>
    <Files "*.asp">
        Require all denied
    </Files>
    <Files "*.sh">
        Require all denied
    </Files>
    <Files "*.cgi">
        Require all denied
    </Files>
    <Files "*.exe">
        Require all denied
    </Files>
    
    # Allow only specific file types
    <FilesMatch "\.(jpg|jpeg|png|gif|pdf|txt|doc|docx)$">
        Require all granted
    </FilesMatch>
</Directory>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Require all denied
</FilesMatch>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # Documents
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/msword "access plus 1 month"
    ExpiresByType application/vnd.openxmlformats-officedocument.wordprocessingml.document "access plus 1 month"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
    
    # Remove browser bugs (only needed for really old browsers)
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
    Header append Vary User-Agent
</IfModule>

# Force HTTPS (uncomment when SSL is configured)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slashes
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} (.+)/$
RewriteRule ^ %1 [R=301,L]

# Custom Error Pages (create these files if needed)
# ErrorDocument 404 /404.php
# ErrorDocument 403 /403.php
# ErrorDocument 500 /500.php

# Prevent server signature disclosure
ServerTokens Prod

# Limit file upload size (adjust as needed)
LimitRequestBody 10485760  # 10MB

# Prevent hotlinking of images (uncomment and adjust domain)
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
# RewriteRule \.(jpg|jpeg|png|gif)$ - [F]
