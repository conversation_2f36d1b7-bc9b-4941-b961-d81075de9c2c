<?php
/**
 * Admin Contacts Management
 * WRANOVSKY Contact Management System
 */

require_once '../config/config.php';
require_admin_login();

// Pagination settings
$items_per_page = get_setting('items_per_page', 20);
$page = max(1, intval($_GET['page'] ?? 1));
$offset = ($page - 1) * $items_per_page;

// Filters
$status_filter = sanitize_input($_GET['status'] ?? '');
$priority_filter = sanitize_input($_GET['priority'] ?? '');
$search_query = sanitize_input($_GET['search'] ?? '');

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $statuses = explode(',', $status_filter);
    $placeholders = str_repeat('?,', count($statuses) - 1) . '?';
    $where_conditions[] = "status IN ($placeholders)";
    $params = array_merge($params, $statuses);
}

if (!empty($priority_filter)) {
    $priorities = explode(',', $priority_filter);
    $placeholders = str_repeat('?,', count($priorities) - 1) . '?';
    $where_conditions[] = "priority IN ($placeholders)";
    $params = array_merge($params, $priorities);
}

if (!empty($search_query)) {
    $where_conditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
    $search_param = '%' . $search_query . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $db = getDB();
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM contact_submissions $where_clause";
    $count_stmt = $db->prepare($count_sql);
    $count_stmt->execute($params);
    $total_items = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_items / $items_per_page);
    
    // Get contacts
    $sql = "
        SELECT id, form_type, first_name, last_name, email, phone, subject, message, 
               status, priority, created_at, updated_at,
               (SELECT COUNT(*) FROM contact_attachments WHERE contact_id = contact_submissions.id) as attachment_count
        FROM contact_submissions 
        $where_clause 
        ORDER BY 
            CASE WHEN priority = 'urgent' THEN 1 
                 WHEN priority = 'high' THEN 2 
                 WHEN priority = 'normal' THEN 3 
                 ELSE 4 END,
            created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute(array_merge($params, [$items_per_page, $offset]));
    $contacts = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Contacts page error: " . $e->getMessage());
    $contacts = [];
    $total_items = 0;
    $total_pages = 0;
}

$page_title = 'Contact Messages';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape_html($page_title); ?> - Admin Panel</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/admin.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h3><?php echo escape_html(COMPANY_NAME); ?></h3>
                <small>Admin Panel</small>
            </div>
            
            <ul class="list-unstyled components">
                <li>
                    <a href="<?php echo ADMIN_URL; ?>/dashboard.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="active">
                    <a href="<?php echo ADMIN_URL; ?>/contacts.php">
                        <i class="fas fa-envelope"></i> Contact Messages
                    </a>
                </li>
                <li>
                    <a href="<?php echo ADMIN_URL; ?>/settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
                <li>
                    <a href="<?php echo ADMIN_URL; ?>/logout.php">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span><?php echo escape_html($_SESSION['admin_name']); ?></span>
                    <small class="d-block text-muted"><?php echo escape_html($_SESSION['admin_role']); ?></small>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div id="content">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-outline-secondary">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <?php echo escape_html($_SESSION['admin_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo ADMIN_URL; ?>/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-0">Contact Messages</h1>
                        <p class="text-muted">Manage and respond to customer inquiries</p>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>New</option>
                                    <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>Read</option>
                                    <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>Replied</option>
                                    <option value="archived" <?php echo $status_filter === 'archived' ? 'selected' : ''; ?>>Archived</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="">All Priorities</option>
                                    <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>Urgent</option>
                                    <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>High</option>
                                    <option value="normal" <?php echo $priority_filter === 'normal' ? 'selected' : ''; ?>>Normal</option>
                                    <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>Low</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo escape_attr($search_query); ?>" 
                                       placeholder="Search by name, email, subject, or message...">
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <?php if (!empty($status_filter) || !empty($priority_filter) || !empty($search_query)): ?>
                        <div class="mt-3">
                            <a href="<?php echo ADMIN_URL; ?>/contacts.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times"></i> Clear Filters
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Results -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            Contact Messages 
                            <span class="badge bg-secondary"><?php echo number_format($total_items); ?></span>
                        </h5>
                    </div>
                    
                    <div class="card-body p-0">
                        <?php if (empty($contacts)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No contact messages found</h5>
                            <p class="text-muted">
                                <?php if (!empty($status_filter) || !empty($priority_filter) || !empty($search_query)): ?>
                                Try adjusting your filters to see more results.
                                <?php else: ?>
                                Contact messages will appear here when customers submit the contact form.
                                <?php endif; ?>
                            </p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Contact</th>
                                        <th>Subject/Message</th>
                                        <th>Status</th>
                                        <th>Priority</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($contacts as $contact): ?>
                                    <tr class="<?php echo $contact['status'] === 'new' ? 'table-warning' : ''; ?>">
                                        <td>
                                            <div>
                                                <strong><?php echo escape_html($contact['first_name'] . ' ' . $contact['last_name']); ?></strong>
                                                <?php if ($contact['attachment_count'] > 0): ?>
                                                <i class="fas fa-paperclip text-muted ms-1" title="Has attachments"></i>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted"><?php echo escape_html($contact['email']); ?></small>
                                            <?php if (!empty($contact['phone'])): ?>
                                            <br><small class="text-muted"><?php echo escape_html($contact['phone']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($contact['subject'])): ?>
                                            <div class="fw-bold"><?php echo escape_html(truncate_text($contact['subject'], 40)); ?></div>
                                            <?php endif; ?>
                                            <small class="text-muted"><?php echo escape_html(truncate_text($contact['message'], 60)); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $contact['status'] === 'new' ? 'warning' : ($contact['status'] === 'read' ? 'info' : ($contact['status'] === 'replied' ? 'success' : 'secondary')); ?>">
                                                <?php echo ucfirst($contact['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $contact['priority'] === 'urgent' ? 'danger' : ($contact['priority'] === 'high' ? 'warning' : ($contact['priority'] === 'normal' ? 'primary' : 'secondary')); ?>">
                                                <?php echo ucfirst($contact['priority']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo format_date($contact['created_at'], 'M j, Y'); ?></small>
                                            <br><small class="text-muted"><?php echo time_ago($contact['created_at']); ?></small>
                                        </td>
                                        <td>
                                            <a href="<?php echo ADMIN_URL; ?>/contact-view.php?id=<?php echo $contact['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <div class="card-footer">
                            <nav aria-label="Contact messages pagination">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Previous</a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Next</a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/admin.js"></script>
</body>
</html>
