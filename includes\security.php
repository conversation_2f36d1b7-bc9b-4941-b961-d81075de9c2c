<?php
/**
 * Security Functions
 * WRANOVSKY Contact Management System
 */

/**
 * Check if user is logged in as admin
 */
function is_admin_logged_in() {
    return isset($_SESSION['admin_id']) && isset($_SESSION['admin_username']);
}

/**
 * Require admin login
 */
function require_admin_login() {
    if (!is_admin_logged_in()) {
        redirect(ADMIN_URL . '/login.php');
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT) {
        admin_logout();
        redirect(ADMIN_URL . '/login.php?timeout=1');
    }
    
    $_SESSION['last_activity'] = time();
}

/**
 * Admin login
 */
function admin_login($username, $password) {
    try {
        $db = getDB();
        $stmt = $db->prepare("
            SELECT id, username, email, password_hash, full_name, role, is_active 
            FROM admin_users 
            WHERE (username = ? OR email = ?) AND is_active = 1
        ");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            // Set session variables
            $_SESSION['admin_id'] = $user['id'];
            $_SESSION['admin_username'] = $user['username'];
            $_SESSION['admin_email'] = $user['email'];
            $_SESSION['admin_name'] = $user['full_name'];
            $_SESSION['admin_role'] = $user['role'];
            $_SESSION['last_activity'] = time();
            
            // Update last login
            $update_stmt = $db->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
            $update_stmt->execute([$user['id']]);
            
            // Log activity
            log_admin_activity($user['id'], 'login', 'system', null, 'Admin logged in');
            
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Admin login error: " . $e->getMessage());
        return false;
    }
}

/**
 * Admin logout
 */
function admin_logout() {
    if (isset($_SESSION['admin_id'])) {
        log_admin_activity($_SESSION['admin_id'], 'logout', 'system', null, 'Admin logged out');
    }
    
    // Clear session variables
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_email']);
    unset($_SESSION['admin_name']);
    unset($_SESSION['admin_role']);
    unset($_SESSION['last_activity']);
    
    // Destroy session if no other data
    if (empty($_SESSION)) {
        session_destroy();
    }
}

/**
 * Check admin role
 */
function check_admin_role($required_role = 'manager') {
    if (!is_admin_logged_in()) {
        return false;
    }
    
    $user_role = $_SESSION['admin_role'] ?? 'manager';
    
    if ($required_role === 'admin') {
        return $user_role === 'admin';
    }
    
    return in_array($user_role, ['admin', 'manager']);
}

/**
 * Require specific admin role
 */
function require_admin_role($required_role = 'manager') {
    require_admin_login();
    
    if (!check_admin_role($required_role)) {
        http_response_code(403);
        die('Access denied. Insufficient permissions.');
    }
}

/**
 * Hash password
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Generate secure random token
 */
function generate_secure_token($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Rate limiting for login attempts
 */
function check_login_rate_limit($identifier, $max_attempts = 5, $time_window = 900) { // 15 minutes
    $cache_key = 'login_attempts_' . md5($identifier);
    
    // Simple file-based rate limiting (in production, use Redis or database)
    $cache_file = sys_get_temp_dir() . '/' . $cache_key;
    
    if (file_exists($cache_file)) {
        $data = json_decode(file_get_contents($cache_file), true);
        $attempts = $data['attempts'] ?? 0;
        $first_attempt = $data['first_attempt'] ?? time();
        
        // Reset if time window has passed
        if (time() - $first_attempt > $time_window) {
            unlink($cache_file);
            return true;
        }
        
        // Check if max attempts exceeded
        if ($attempts >= $max_attempts) {
            return false;
        }
    }
    
    return true;
}

/**
 * Record login attempt
 */
function record_login_attempt($identifier, $success = false) {
    $cache_key = 'login_attempts_' . md5($identifier);
    $cache_file = sys_get_temp_dir() . '/' . $cache_key;
    
    if ($success) {
        // Remove rate limit on successful login
        if (file_exists($cache_file)) {
            unlink($cache_file);
        }
        return;
    }
    
    // Record failed attempt
    $data = ['attempts' => 1, 'first_attempt' => time()];
    
    if (file_exists($cache_file)) {
        $existing_data = json_decode(file_get_contents($cache_file), true);
        $data['attempts'] = ($existing_data['attempts'] ?? 0) + 1;
        $data['first_attempt'] = $existing_data['first_attempt'] ?? time();
    }
    
    file_put_contents($cache_file, json_encode($data));
}

/**
 * Validate and sanitize file upload
 */
function secure_file_upload($file, $upload_dir) {
    $errors = validate_file_upload($file);
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    // Generate secure filename
    $original_name = $file['name'];
    $secure_name = generate_unique_filename($original_name);
    $upload_path = $upload_dir . '/' . $secure_name;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return [
            'success' => true,
            'original_name' => $original_name,
            'stored_name' => $secure_name,
            'file_path' => $upload_path,
            'file_size' => $file['size'],
            'mime_type' => mime_content_type($upload_path)
        ];
    }
    
    return ['success' => false, 'errors' => ['Failed to upload file.']];
}

/**
 * Clean old temporary files
 */
function clean_temp_files($temp_dir, $max_age = 3600) { // 1 hour
    if (!is_dir($temp_dir)) {
        return;
    }
    
    $files = glob($temp_dir . '/*');
    $now = time();
    
    foreach ($files as $file) {
        if (is_file($file) && ($now - filemtime($file)) > $max_age) {
            unlink($file);
        }
    }
}

/**
 * Escape output for HTML
 */
function escape_html($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Escape output for HTML attributes
 */
function escape_attr($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Escape output for JavaScript
 */
function escape_js($string) {
    return json_encode($string, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT);
}
?>
