/* WRANOVSKY Website Styles */

:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --dark-color: #2c3e50;
  --light-color: #f8f9fa;
  --text-color: #333;
  --border-color: #dee2e6;
}

/* Global Styles */
body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  padding-top: 76px; /* Account for fixed navbar */
}

/* Navigation */
.navbar {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand img {
  transition: all 0.3s ease;
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
  width: 80%;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-section .container {
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--border-color);
}

/* Cards */
.card {
  border: none;
  border-radius: 15px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  font-weight: 600;
}

/* Buttons */
.btn {
  border-radius: 25px;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--primary-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-outline-primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* Forms */
.form-control {
  border-radius: 10px;
  border: 2px solid var(--border-color);
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

/* Feature Icons */
.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: 50%;
  color: white;
}

/* Footer */
footer {
  background: var(--dark-color) !important;
}

footer a {
  transition: all 0.3s ease;
}

footer a:hover {
  color: var(--primary-color) !important;
}

/* Social Links */
.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--primary-color);
  transform: translateY(-3px);
}

/* Scroll to Top Button */
#btn-back-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: none;
  z-index: 1000;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

#btn-back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    text-align: center;
  }

  .hero-section .display-4 {
    font-size: 2rem;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Contact Form Specific */
.contact-info h6 {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.contact-info p {
  margin-bottom: 1rem;
}

/* Alert Enhancements */
.alert {
  border-radius: 10px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-success {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
}

.alert-warning {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Custom Utilities */
.text-gradient {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-gradient {
  border: 2px solid;
  border-image: linear-gradient(
      135deg,
      var(--primary-color),
      var(--secondary-color)
    )
    1;
}

.shadow-custom {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .navbar-nav {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .navbar-nav .nav-link::after {
  left: auto;
  right: 50%;
  transform: translateX(50%);
}

[dir="rtl"] .btn {
  text-align: center;
}

[dir="rtl"] .feature-icon {
  margin-left: auto;
  margin-right: auto;
}

[dir="rtl"] .social-links a {
  margin-left: 1rem;
  margin-right: 0;
}

[dir="rtl"] #btn-back-to-top {
  left: 20px;
  right: auto;
}

[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
}

/* Arabic Font Support */
[dir="rtl"] {
  font-family: "Segoe UI", "Tahoma", "Arial", "Helvetica Neue", sans-serif;
}

[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
  font-weight: 600;
}

/* Print Styles */
@media print {
  .navbar,
  .btn,
  #btn-back-to-top,
  footer {
    display: none !important;
  }

  body {
    padding-top: 0;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}
