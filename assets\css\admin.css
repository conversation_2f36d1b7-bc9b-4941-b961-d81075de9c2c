/* Admin Panel Styles */

:root {
    --admin-primary: #667eea;
    --admin-secondary: #764ba2;
    --admin-sidebar-bg: #2c3e50;
    --admin-sidebar-text: #bdc3c7;
    --admin-sidebar-active: #3498db;
    --admin-content-bg: #f8f9fa;
    --admin-border: #dee2e6;
}

/* Layout */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    min-width: 250px;
    max-width: 250px;
    background: var(--admin-sidebar-bg);
    color: var(--admin-sidebar-text);
    transition: all 0.3s;
    position: relative;
    z-index: 1000;
}

.sidebar.active {
    margin-left: -250px;
}

.sidebar-header {
    padding: 20px;
    background: rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h3 {
    color: white;
    font-size: 1.2rem;
    margin: 0;
    font-weight: 600;
}

.sidebar-header small {
    color: var(--admin-sidebar-text);
    font-size: 0.8rem;
}

.sidebar ul.components {
    padding: 20px 0;
}

.sidebar ul li {
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.sidebar ul li a {
    padding: 15px 20px;
    font-size: 1rem;
    display: block;
    color: var(--admin-sidebar-text);
    text-decoration: none;
    transition: all 0.3s;
    position: relative;
}

.sidebar ul li a:hover,
.sidebar ul li.active > a {
    color: white;
    background: var(--admin-sidebar-active);
}

.sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar ul li a .badge {
    float: right;
    margin-top: 2px;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.user-info {
    text-align: center;
}

.user-info i {
    font-size: 2rem;
    margin-bottom: 5px;
    color: var(--admin-sidebar-active);
}

.user-info span {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 0.9rem;
}

.user-info small {
    color: var(--admin-sidebar-text);
    font-size: 0.8rem;
}

/* Content */
#content {
    width: 100%;
    min-height: 100vh;
    transition: all 0.3s;
    background: var(--admin-content-bg);
}

#content.active {
    margin-left: -250px;
}

/* Top Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#sidebarCollapse {
    border: none;
    background: transparent;
    color: #666;
}

#sidebarCollapse:hover {
    background: #f8f9fa;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background: white;
    border-bottom: 1px solid var(--admin-border);
    font-weight: 600;
    color: #333;
}

/* Statistics Cards */
.border-left-primary {
    border-left: 4px solid var(--admin-primary) !important;
}

.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.border-left-danger {
    border-left: 4px solid #dc3545 !important;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-primary {
    color: var(--admin-primary) !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #333;
    background: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--admin-secondary), var(--admin-primary));
    transform: translateY(-1px);
}

.btn-outline-primary {
    border-color: var(--admin-primary);
    color: var(--admin-primary);
}

.btn-outline-primary:hover {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
}

/* Forms */
.form-control {
    border-radius: 6px;
    border: 1px solid var(--admin-border);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select {
    border-radius: 6px;
    border: 1px solid var(--admin-border);
}

.form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Pagination */
.pagination .page-link {
    color: var(--admin-primary);
    border-color: var(--admin-border);
}

.pagination .page-item.active .page-link {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
}

.pagination .page-link:hover {
    color: var(--admin-secondary);
    background-color: rgba(102, 126, 234, 0.1);
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px;
    }
    
    .sidebar.active {
        margin-left: 0;
    }
    
    #content {
        width: 100%;
    }
    
    #content.active {
        margin-left: 0;
    }
    
    .wrapper {
        position: relative;
    }
    
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1050;
    }
    
    .sidebar.active {
        box-shadow: 3px 0 10px rgba(0,0,0,0.3);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* Animation */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.sidebar.show {
    animation: slideInLeft 0.3s ease-out;
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn {
        display: none !important;
    }
    
    #content {
        width: 100% !important;
        margin: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
