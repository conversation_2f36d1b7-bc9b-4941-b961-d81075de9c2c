# Multi-Language Support Guide

## Overview
The WRANOVSKY website now supports both **English** and **Arabic** with complete RTL (Right-to-Left) support for Arabic.

## Features Implemented

### 1. **Language System**
- **Automatic Detection**: Detects browser language preference
- **Session Storage**: Remembers user's language choice
- **URL Parameters**: Switch language with `?lang=en` or `?lang=ar`
- **Fallback**: Defaults to English if language not supported

### 2. **Translation System**
- **Complete Translations**: All text translated to Arabic
- **Template Functions**: `t()` and `tf()` functions for translations
- **Parameter Support**: Dynamic content with placeholders

### 3. **RTL Support**
- **Direction Detection**: Automatic RTL layout for Arabic
- **CSS Adaptations**: Proper spacing, alignment, and positioning
- **Bootstrap RTL**: Compatible with Bootstrap 5 RTL features
- **Font Support**: Optimized fonts for Arabic text

### 4. **Navigation**
- **Language Switcher**: Dropdown in navigation bar
- **Current Language Display**: Shows active language
- **URL Preservation**: Maintains current page when switching languages

## How to Use

### **For Users:**
1. **Switch Language**: Click language dropdown in navigation
2. **Direct URLs**: Add `?lang=ar` for Arabic or `?lang=en` for English
3. **Automatic**: Browser language detected on first visit

### **For Developers:**

#### **Adding New Translations:**
1. **Edit Language Files**:
   - English: `languages/en.php`
   - Arabic: `languages/ar.php`

2. **Add Translation Key**:
   ```php
   // In languages/en.php
   'new_key' => 'English Text',
   
   // In languages/ar.php
   'new_key' => 'النص العربي',
   ```

3. **Use in Templates**:
   ```php
   <?php echo t('new_key'); ?>
   ```

#### **Translation Functions:**

```php
// Simple translation
t('key_name')

// Translation with fallback
t('key_name', 'Default text if key not found')

// Translation with parameters
tf('welcome_message', ['name' => 'John'], 'Welcome!')
// Use {name} in translation files
```

#### **Language Helper Functions:**

```php
// Get current language (en/ar)
get_current_language()

// Check if RTL
is_rtl()

// Get language direction
get_language_direction() // 'ltr' or 'rtl'

// Get language name
get_language_name() // 'English' or 'العربية'

// Generate language URLs
get_language_url('ar') // URL with Arabic parameter
```

#### **CSS Classes for RTL:**

```php
// Text alignment
<?php echo text_align_class(); ?> // 'text-start' or 'text-end'

// Float direction
<?php echo float_class('start'); ?> // Adjusts for RTL

// Margin/Padding
<?php echo margin_class('s', '3'); ?> // Responsive margins
```

## File Structure

```
languages/
├── en.php          # English translations
├── ar.php          # Arabic translations
└── [future].php    # Additional languages

includes/
└── language.php    # Language system core

assets/css/
└── style.css       # RTL CSS support
```

## Translation Keys

### **Navigation**
- `nav_home`, `nav_about`, `nav_products`, `nav_projects`, `nav_news`, `nav_contact`

### **Homepage**
- `hero_title`, `hero_subtitle`, `hero_cta`
- `features_title`, `features_subtitle`
- `feature_quality_title`, `feature_handcrafted_title`, `feature_custom_title`

### **Contact Form**
- `form_first_name`, `form_last_name`, `form_email`, `form_phone`
- `form_subject`, `form_message`, `form_attachments`, `form_send`

### **Common**
- `view_collection`, `inquire`, `learn_more`, `get_quote`, `call_us`

## Adding New Languages

### **1. Create Language File**
```php
// languages/fr.php (French example)
<?php
return [
    'nav_home' => 'Accueil',
    'nav_about' => 'À propos',
    // ... more translations
];
```

### **2. Update Configuration**
```php
// includes/language.php
define('SUPPORTED_LANGUAGES', ['en', 'ar', 'fr']);
```

### **3. Add to Language Switcher**
```php
// In navigation template
<li><a class="dropdown-item" href="<?php echo get_language_url('fr'); ?>">Français</a></li>
```

## RTL Considerations

### **CSS Adaptations**
- **Text Direction**: Automatic with `dir="rtl"`
- **Margins/Padding**: Use logical properties
- **Positioning**: Left/right adjustments
- **Icons**: Mirror horizontally if needed

### **Content Considerations**
- **Numbers**: Can be displayed in Arabic numerals
- **Dates**: Formatted according to locale
- **Images**: Consider text direction in graphics

## Testing

### **Language Switching**
1. Visit homepage: `http://localhost/wranovsky`
2. Switch to Arabic: Click "العربية" in navigation
3. Verify RTL layout and Arabic text
4. Test all pages and forms

### **URL Parameters**
- `?lang=en` - Force English
- `?lang=ar` - Force Arabic
- No parameter - Auto-detect or session

### **Form Submissions**
- Test contact form in both languages
- Verify validation messages
- Check email notifications

## Browser Support

### **RTL Support**
- **Modern Browsers**: Full RTL support
- **CSS Grid/Flexbox**: Automatic direction handling
- **Bootstrap 5**: Built-in RTL support

### **Font Rendering**
- **Arabic**: Proper font selection and rendering
- **Mixed Content**: English + Arabic text handling

## Performance

### **Optimization**
- **Language Files**: Cached after first load
- **CSS**: Single file with RTL rules
- **No JavaScript**: Pure PHP/CSS solution

### **SEO Considerations**
- **Language Attributes**: Proper `lang` and `dir` attributes
- **URL Structure**: Language parameters preserved
- **Content**: Fully translated for search engines

## Troubleshooting

### **Common Issues**

1. **Text Not Translating**
   - Check translation key exists in language file
   - Verify `t()` function usage
   - Clear browser cache

2. **RTL Layout Issues**
   - Check `dir="rtl"` attribute
   - Verify CSS RTL rules
   - Test with browser developer tools

3. **Language Not Switching**
   - Check URL parameters
   - Verify session storage
   - Clear browser cookies

### **Debug Mode**
```php
// Add to config for debugging
var_dump(get_current_language());
var_dump($translations);
```

## Future Enhancements

### **Possible Additions**
- **More Languages**: French, German, Spanish
- **Admin Translation**: Multi-language admin panel
- **Database Content**: Translatable database content
- **SEO URLs**: Language-specific URLs (/en/, /ar/)
- **Currency**: Multi-currency support
- **Date/Time**: Locale-specific formatting
