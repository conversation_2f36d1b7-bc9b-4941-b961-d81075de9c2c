/**
 * Admin Panel JavaScript
 * WRANOVSKY Contact Management System
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Sidebar toggle functionality
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');
    
    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            content.classList.toggle('active');
            
            // Save sidebar state
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('active'));
        });
    }
    
    // Restore sidebar state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed && sidebar && content) {
        sidebar.classList.add('active');
        content.classList.add('active');
    }
    
    // Auto-hide alerts
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // DataTable initialization (if DataTables is loaded)
    if (typeof DataTable !== 'undefined') {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(function(table) {
            new DataTable(table, {
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        });
    }
    
    // Confirm dialogs for dangerous actions
    const dangerousActions = document.querySelectorAll('[data-confirm]');
    dangerousActions.forEach(function(element) {
        element.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
    
    // Form validation enhancements
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // Focus on first invalid field
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
            form.classList.add('was-validated');
        });
    });
    
    // AJAX form submissions
    const ajaxForms = document.querySelectorAll('.ajax-form');
    ajaxForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitFormAjax(this);
        });
    });
    
    // Status update functionality
    const statusSelects = document.querySelectorAll('.status-update');
    statusSelects.forEach(function(select) {
        select.addEventListener('change', function() {
            updateContactStatus(this.dataset.contactId, this.value);
        });
    });
    
    // Priority update functionality
    const prioritySelects = document.querySelectorAll('.priority-update');
    prioritySelects.forEach(function(select) {
        select.addEventListener('change', function() {
            updateContactPriority(this.dataset.contactId, this.value);
        });
    });
    
    // Bulk actions
    const bulkActionForm = document.getElementById('bulkActionForm');
    if (bulkActionForm) {
        const selectAllCheckbox = document.getElementById('selectAll');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const bulkActionSelect = document.getElementById('bulkAction');
        const bulkActionBtn = document.getElementById('bulkActionBtn');
        
        // Select all functionality
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                updateBulkActionButton();
            });
        }
        
        // Individual checkbox change
        itemCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                updateBulkActionButton();
                
                // Update select all checkbox
                if (selectAllCheckbox) {
                    const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
                    selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
                }
            });
        });
        
        // Bulk action execution
        if (bulkActionBtn) {
            bulkActionBtn.addEventListener('click', function() {
                const selectedItems = Array.from(document.querySelectorAll('.item-checkbox:checked')).map(cb => cb.value);
                const action = bulkActionSelect.value;
                
                if (selectedItems.length === 0) {
                    alert('Please select at least one item.');
                    return;
                }
                
                if (!action) {
                    alert('Please select an action.');
                    return;
                }
                
                if (confirm(`Are you sure you want to ${action} ${selectedItems.length} item(s)?`)) {
                    executeBulkAction(action, selectedItems);
                }
            });
        }
    }
    
    // Real-time notifications (if WebSocket is available)
    if (typeof WebSocket !== 'undefined') {
        initializeNotifications();
    }
    
    // Auto-refresh for dashboard
    if (document.body.classList.contains('dashboard-page')) {
        setInterval(refreshDashboardStats, 300000); // 5 minutes
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S to save forms
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            const activeForm = document.querySelector('form:focus-within');
            if (activeForm) {
                e.preventDefault();
                activeForm.submit();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) modal.hide();
            }
        }
    });
});

// Utility functions
function submitFormAjax(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Success!', data.message, 'success');
            if (data.redirect) {
                window.location.href = data.redirect;
            }
        } else {
            showNotification('Error!', data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error!', 'An unexpected error occurred.', 'error');
    })
    .finally(() => {
        // Restore button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

function updateContactStatus(contactId, status) {
    fetch('/admin/ajax/update-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            contact_id: contactId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Status Updated', data.message, 'success');
        } else {
            showNotification('Error', data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error', 'Failed to update status.', 'error');
    });
}

function updateContactPriority(contactId, priority) {
    fetch('/admin/ajax/update-priority.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            contact_id: contactId,
            priority: priority
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Priority Updated', data.message, 'success');
        } else {
            showNotification('Error', data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error', 'Failed to update priority.', 'error');
    });
}

function updateBulkActionButton() {
    const selectedCount = document.querySelectorAll('.item-checkbox:checked').length;
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    
    if (bulkActionBtn) {
        bulkActionBtn.disabled = selectedCount === 0;
        bulkActionBtn.textContent = selectedCount > 0 ? `Apply to ${selectedCount} item(s)` : 'Apply Action';
    }
}

function executeBulkAction(action, selectedItems) {
    fetch('/admin/ajax/bulk-action.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: action,
            items: selectedItems
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Bulk Action Completed', data.message, 'success');
            // Refresh the page or update the table
            window.location.reload();
        } else {
            showNotification('Error', data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error', 'Failed to execute bulk action.', 'error');
    });
}

function showNotification(title, message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <strong>${title}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function refreshDashboardStats() {
    fetch('/admin/ajax/dashboard-stats.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update dashboard statistics
            Object.keys(data.stats).forEach(key => {
                const element = document.getElementById(`stat-${key}`);
                if (element) {
                    element.textContent = data.stats[key];
                }
            });
        }
    })
    .catch(error => {
        console.error('Error refreshing dashboard stats:', error);
    });
}

function initializeNotifications() {
    // WebSocket connection for real-time notifications
    // Implementation would depend on your WebSocket setup
    console.log('Real-time notifications initialized');
}
