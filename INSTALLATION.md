# WRANOVSKY Contact Management System - Installation Guide

## Overview
This guide will help you install and configure the WRANOVSKY Contact Management System, a pure PHP application with MySQL database for managing contact form submissions through an admin dashboard.

## System Requirements

### Server Requirements
- **PHP**: 7.4 or higher (8.0+ recommended)
- **MySQL**: 5.7 or higher (8.0+ recommended)
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Memory**: Minimum 128MB PHP memory limit
- **Storage**: At least 100MB free space

### PHP Extensions Required
- PDO MySQL
- mbstring
- fileinfo
- openssl
- session
- json

### Optional (Recommended)
- mod_rewrite (Apache) or equivalent URL rewriting
- SSL certificate for HTTPS
- Composer (for future dependency management)

## Installation Steps

### 1. Download and Extract Files
1. Download the project files
2. Extract to your web server directory (e.g., `/var/www/html/wranovsky/` or `C:\xampp\htdocs\wranovsky\`)

### 2. Database Setup

#### Create Database
```sql
-- Connect to MySQL as root or admin user
mysql -u root -p

-- Create database
CREATE DATABASE wranovsky_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create database user (recommended for security)
CREATE USER 'wranovsky_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON wranovsky_cms.* TO 'wranovsky_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Import Database Schema
```bash
# Import the database schema
mysql -u wranovsky_user -p wranovsky_cms < database_schema.sql
```

### 3. Configuration

#### Database Configuration
Edit `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'wranovsky_cms');
define('DB_USER', 'wranovsky_user');
define('DB_PASS', 'your_secure_password');
```

#### Main Configuration
Edit `config/config.php`:
```php
// Update these settings
define('SITE_URL', 'https://yourdomain.com'); // Your website URL
define('SMTP_HOST', 'smtp.gmail.com'); // Your SMTP server
define('SMTP_USERNAME', '<EMAIL>'); // Your email
define('SMTP_PASSWORD', 'your-app-password'); // Your email password
define('FROM_EMAIL', '<EMAIL>'); // From email address
```

### 4. File Permissions

#### Linux/Unix Systems
```bash
# Set proper permissions
chmod 755 /path/to/wranovsky/
chmod -R 644 /path/to/wranovsky/*
chmod -R 755 /path/to/wranovsky/uploads/
chmod 644 /path/to/wranovsky/config/*.php

# Make uploads directory writable
chown -R www-data:www-data /path/to/wranovsky/uploads/
```

#### Windows Systems
- Ensure the `uploads` folder has write permissions for the web server user
- Right-click → Properties → Security → Edit → Add write permissions

### 5. Web Server Configuration

#### Apache (.htaccess)
Create `.htaccess` in the root directory:
```apache
RewriteEngine On

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Hide sensitive files
<Files "*.sql">
    Deny from all
</Files>

<Files "*.md">
    Deny from all
</Files>

<FilesMatch "^\.">
    Deny from all
</FilesMatch>

# Protect config directory
<Directory "config">
    Deny from all
</Directory>

# Protect uploads directory from script execution
<Directory "uploads">
    <Files "*.php">
        Deny from all
    </Files>
    <Files "*.phtml">
        Deny from all
    </Files>
    <Files "*.pl">
        Deny from all
    </Files>
    <Files "*.py">
        Deny from all
    </Files>
    <Files "*.jsp">
        Deny from all
    </Files>
    <Files "*.asp">
        Deny from all
    </Files>
    <Files "*.sh">
        Deny from all
    </Files>
    <Files "*.cgi">
        Deny from all
    </Files>
</Directory>
```

#### Nginx Configuration
Add to your Nginx server block:
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/wranovsky;
    index index.php index.html;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Hide sensitive files
    location ~ \.(sql|md)$ {
        deny all;
    }

    location ~ /\. {
        deny all;
    }

    # Protect config directory
    location ^~ /config/ {
        deny all;
    }

    # Protect uploads from script execution
    location ^~ /uploads/ {
        location ~ \.(php|phtml|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 6. SSL Certificate (Recommended)
- Install SSL certificate for HTTPS
- Update `SITE_URL` in config to use `https://`
- Redirect HTTP to HTTPS

### 7. Default Admin Account

The system creates a default admin account:
- **Username**: admin
- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ IMPORTANT**: Change this password immediately after first login!

## Post-Installation Steps

### 1. First Login
1. Navigate to `https://yourdomain.com/admin/login.php`
2. Login with default credentials
3. Change the admin password immediately

### 2. Update Company Information
1. Go to Admin → Settings
2. Update company details, contact information
3. Configure email settings
4. Set file upload limits

### 3. Test Contact Form
1. Visit the main website
2. Fill out the contact form
3. Check if submission appears in admin dashboard
4. Test email notifications

### 4. Security Checklist
- [ ] Changed default admin password
- [ ] Updated all configuration files
- [ ] Set proper file permissions
- [ ] Configured SSL certificate
- [ ] Tested file upload restrictions
- [ ] Verified email functionality
- [ ] Checked error logs

## Troubleshooting

### Common Issues

#### Database Connection Error
- Check database credentials in `config/database.php`
- Verify MySQL service is running
- Ensure database user has proper permissions

#### File Upload Issues
- Check `uploads/` directory permissions
- Verify PHP `upload_max_filesize` and `post_max_size` settings
- Check web server write permissions

#### Email Not Sending
- Verify SMTP settings in `config/config.php`
- Check if your hosting provider blocks SMTP
- Test with a different email provider

#### Admin Login Issues
- Clear browser cache and cookies
- Check session configuration in PHP
- Verify file permissions on session directory

### Log Files
- PHP errors: Check your server's PHP error log
- Application errors: Check `error_log` files in the application directory
- Web server errors: Check Apache/Nginx error logs

## Maintenance

### Regular Tasks
- **Backup database** regularly
- **Update PHP** and MySQL versions
- **Monitor disk space** for uploads
- **Review security logs**
- **Clean old temporary files**

### Database Backup
```bash
# Create backup
mysqldump -u wranovsky_user -p wranovsky_cms > backup_$(date +%Y%m%d).sql

# Restore backup
mysql -u wranovsky_user -p wranovsky_cms < backup_20231201.sql
```

### File Backup
```bash
# Create full backup
tar -czf wranovsky_backup_$(date +%Y%m%d).tar.gz /path/to/wranovsky/
```

## Support

For technical support or questions:
- Check the `DATABASE_DESIGN.md` file for database schema details
- Review PHP error logs for debugging
- Ensure all system requirements are met

## Security Notes

- Always use HTTPS in production
- Regularly update PHP and MySQL
- Monitor file uploads for malicious content
- Use strong passwords for admin accounts
- Consider implementing two-factor authentication
- Regular security audits and updates
