<?php
/**
 * Main Configuration File
 * WRANOVSKY Contact Management System
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Europe/Prague');

// Site configuration
define('SITE_NAME', 'WRANOVSKY - Bohemian Crystal Chandeliers');
define('SITE_URL', 'https://wranovsky.com'); // Change to your domain
define('ADMIN_URL', SITE_URL . '/admin');

// Paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// URLs
define('ASSETS_URL', SITE_URL . '/assets');
define('UPLOADS_URL', SITE_URL . '/uploads');

// Security
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'txt', 'pdf', 'doc', 'docx']);

// Email settings
define('SMTP_HOST', 'smtp.gmail.com'); // Change to your SMTP server
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // Change to your email
define('SMTP_PASSWORD', 'your-app-password'); // Change to your password
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'WRANOVSKY Team');

// Company information
define('COMPANY_NAME', 'WRANOVSKY');
define('COMPANY_FULL_NAME', 'WRANOVSKY - Bohemian Crystal Chandeliers Manufacturer');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_PHONE', '+*********** 236');
define('COMPANY_ADDRESS', 'Vesecko, Turnov, Liberecký kraj, 511 01, Czech Republic');

// Include required files
require_once ROOT_PATH . '/config/database.php';
require_once INCLUDES_PATH . '/functions.php';
require_once INCLUDES_PATH . '/security.php';
require_once INCLUDES_PATH . '/language.php';

// Create upload directory if it doesn't exist
if (!file_exists(UPLOADS_PATH)) {
    mkdir(UPLOADS_PATH, 0755, true);
}

// Create uploads subdirectories
$upload_dirs = ['contacts', 'temp'];
foreach ($upload_dirs as $dir) {
    $full_path = UPLOADS_PATH . '/' . $dir;
    if (!file_exists($full_path)) {
        mkdir($full_path, 0755, true);
    }
}
?>
