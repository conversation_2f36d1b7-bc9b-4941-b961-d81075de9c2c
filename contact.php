<?php
/**
 * Contact Page
 * WRANOVSKY Contact Management System
 */

require_once 'config/config.php';

// Page variables
$page_title = 'Contact Us';
$page_description = 'Get in touch with WRANOVSKY for inquiries about our Bohemian crystal chandeliers, custom lighting solutions, and more.';
$page_header = true;
$page_breadcrumb = [
    ['title' => 'Contact Us']
];

// Handle form submission
$form_errors = [];
$form_success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $form_errors[] = 'Security token mismatch. Please try again.';
    } else {
        // Sanitize and validate input
        $first_name = sanitize_input($_POST['first_name'] ?? '');
        $last_name = sanitize_input($_POST['last_name'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        $phone = sanitize_input($_POST['phone'] ?? '');
        $subject = sanitize_input($_POST['subject'] ?? '');
        $message = sanitize_input($_POST['message'] ?? '');
        $form_type = sanitize_input($_POST['form_type'] ?? 'general');
        $product_title = sanitize_input($_POST['product_title'] ?? '');
        $product_url = sanitize_input($_POST['product_url'] ?? '');
        
        // Validation
        if (empty($first_name)) {
            $form_errors[] = 'First name is required.';
        }
        if (empty($last_name)) {
            $form_errors[] = 'Last name is required.';
        }
        if (empty($email)) {
            $form_errors[] = 'Email address is required.';
        } elseif (!validate_email($email)) {
            $form_errors[] = 'Please enter a valid email address.';
        }
        if (empty($message)) {
            $form_errors[] = 'Message is required.';
        }
        
        // If no errors, save to database
        if (empty($form_errors)) {
            try {
                $db = getDB();
                
                // Insert contact submission
                $stmt = $db->prepare("
                    INSERT INTO contact_submissions 
                    (form_type, first_name, last_name, email, phone, subject, message, product_title, product_url, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $form_type,
                    $first_name,
                    $last_name,
                    $email,
                    $phone,
                    $subject,
                    $message,
                    $product_title,
                    $product_url,
                    $_SERVER['REMOTE_ADDR'] ?? null,
                    $_SERVER['HTTP_USER_AGENT'] ?? null
                ]);
                
                $contact_id = $db->lastInsertId();
                
                // Handle file uploads if any
                if (!empty($_FILES['attachments']['name'][0])) {
                    $upload_dir = UPLOADS_PATH . '/contacts';
                    
                    for ($i = 0; $i < count($_FILES['attachments']['name']); $i++) {
                        if ($_FILES['attachments']['error'][$i] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['attachments']['name'][$i],
                                'type' => $_FILES['attachments']['type'][$i],
                                'tmp_name' => $_FILES['attachments']['tmp_name'][$i],
                                'error' => $_FILES['attachments']['error'][$i],
                                'size' => $_FILES['attachments']['size'][$i]
                            ];
                            
                            $upload_result = secure_file_upload($file, $upload_dir);
                            
                            if ($upload_result['success']) {
                                // Save file info to database
                                $file_stmt = $db->prepare("
                                    INSERT INTO contact_attachments 
                                    (contact_id, original_filename, stored_filename, file_path, file_size, mime_type, file_extension) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?)
                                ");
                                
                                $file_stmt->execute([
                                    $contact_id,
                                    $upload_result['original_name'],
                                    $upload_result['stored_name'],
                                    $upload_result['file_path'],
                                    $upload_result['file_size'],
                                    $upload_result['mime_type'],
                                    pathinfo($upload_result['original_name'], PATHINFO_EXTENSION)
                                ]);
                            }
                        }
                    }
                }
                
                $form_success = true;
                $_SESSION['success_message'] = 'Thank you for your message! We will get back to you within 24-48 hours.';
                
                // Redirect to prevent form resubmission
                redirect(SITE_URL . '/contact.php?sent=1');
                
            } catch (Exception $e) {
                error_log("Contact form error: " . $e->getMessage());
                $form_errors[] = 'An error occurred while sending your message. Please try again.';
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-lg-8 mb-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">Send us a Message</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($form_errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($form_errors as $error): ?>
                        <li><?php echo escape_html($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="form_type" value="general">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="<?php echo escape_attr($_POST['first_name'] ?? ''); ?>" required>
                            <div class="invalid-feedback">Please provide your first name.</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="<?php echo escape_attr($_POST['last_name'] ?? ''); ?>" required>
                            <div class="invalid-feedback">Please provide your last name.</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo escape_attr($_POST['email'] ?? ''); ?>" required>
                            <div class="invalid-feedback">Please provide a valid email address.</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo escape_attr($_POST['phone'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="subject" name="subject" 
                               value="<?php echo escape_attr($_POST['subject'] ?? ''); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">Message *</label>
                        <textarea class="form-control" id="message" name="message" rows="5" required><?php echo escape_html($_POST['message'] ?? ''); ?></textarea>
                        <div class="invalid-feedback">Please provide your message.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="attachments" class="form-label">Attachments (Optional)</label>
                        <input type="file" class="form-control" id="attachments" name="attachments[]" multiple 
                               accept=".jpg,.jpeg,.png,.gif,.txt,.pdf,.doc,.docx">
                        <div class="form-text">
                            Allowed file types: JPG, PNG, GIF, TXT, PDF, DOC, DOCX. Maximum size: <?php echo format_file_size(MAX_FILE_SIZE); ?> per file.
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>Send Message
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">Contact Information</h4>
            </div>
            <div class="card-body">
                <div class="contact-info">
                    <div class="mb-3">
                        <h6><i class="fas fa-map-marker-alt text-primary me-2"></i>Address</h6>
                        <p class="mb-0"><?php echo escape_html(COMPANY_ADDRESS); ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-phone text-primary me-2"></i>Phone</h6>
                        <p class="mb-0">
                            <a href="tel:<?php echo escape_attr(str_replace(' ', '', COMPANY_PHONE)); ?>" class="text-decoration-none">
                                <?php echo escape_html(COMPANY_PHONE); ?>
                            </a>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-envelope text-primary me-2"></i>Email</h6>
                        <p class="mb-0">
                            <a href="mailto:<?php echo escape_attr(COMPANY_EMAIL); ?>" class="text-decoration-none">
                                <?php echo escape_html(COMPANY_EMAIL); ?>
                            </a>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-clock text-primary me-2"></i>Business Hours</h6>
                        <p class="mb-1">Monday - Friday: 9:00 AM - 6:00 PM</p>
                        <p class="mb-0">Saturday: 10:00 AM - 4:00 PM</p>
                        <p class="mb-0">Sunday: Closed</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">Quick Links</h4>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><a href="<?php echo SITE_URL; ?>/products.php" class="text-decoration-none">View Our Products</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/projects.php" class="text-decoration-none">See Our Projects</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/about.php" class="text-decoration-none">About Our Company</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/news.php" class="text-decoration-none">Latest News</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
