<?php
/**
 * Admin Dashboard
 * WRANOVSKY Contact Management System
 */

require_once '../config/config.php';
require_admin_login();

// Get dashboard statistics
try {
    $db = getDB();
    
    // Total contacts
    $stmt = $db->query("SELECT COUNT(*) as total FROM contact_submissions");
    $total_contacts = $stmt->fetch()['total'];
    
    // New contacts (last 7 days)
    $stmt = $db->query("SELECT COUNT(*) as new_contacts FROM contact_submissions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $new_contacts = $stmt->fetch()['new_contacts'];
    
    // Unread contacts
    $stmt = $db->query("SELECT COUNT(*) as unread FROM contact_submissions WHERE status = 'new'");
    $unread_contacts = $stmt->fetch()['unread'];
    
    // High priority contacts
    $stmt = $db->query("SELECT COUNT(*) as high_priority FROM contact_submissions WHERE priority IN ('high', 'urgent') AND status != 'archived'");
    $high_priority = $stmt->fetch()['high_priority'];
    
    // Recent contacts
    $stmt = $db->query("
        SELECT id, first_name, last_name, email, subject, message, status, priority, created_at 
        FROM contact_submissions 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent_contacts = $stmt->fetchAll();
    
    // Contact statistics by status
    $stmt = $db->query("
        SELECT status, COUNT(*) as count 
        FROM contact_submissions 
        GROUP BY status
    ");
    $status_stats = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $total_contacts = $new_contacts = $unread_contacts = $high_priority = 0;
    $recent_contacts = $status_stats = [];
}

$page_title = 'Dashboard';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape_html($page_title); ?> - Admin Panel</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/admin.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h3><?php echo escape_html(COMPANY_NAME); ?></h3>
                <small>Admin Panel</small>
            </div>
            
            <ul class="list-unstyled components">
                <li class="active">
                    <a href="<?php echo ADMIN_URL; ?>/dashboard.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li>
                    <a href="<?php echo ADMIN_URL; ?>/contacts.php">
                        <i class="fas fa-envelope"></i> Contact Messages
                        <?php if ($unread_contacts > 0): ?>
                        <span class="badge bg-danger"><?php echo $unread_contacts; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="<?php echo ADMIN_URL; ?>/settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
                <li>
                    <a href="<?php echo ADMIN_URL; ?>/logout.php">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span><?php echo escape_html($_SESSION['admin_name']); ?></span>
                    <small class="d-block text-muted"><?php echo escape_html($_SESSION['admin_role']); ?></small>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div id="content">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-outline-secondary">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <?php echo escape_html($_SESSION['admin_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo ADMIN_URL; ?>/profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item" href="<?php echo ADMIN_URL; ?>/settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo ADMIN_URL; ?>/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-0">Dashboard</h1>
                        <p class="text-muted">Welcome back, <?php echo escape_html($_SESSION['admin_name']); ?>!</p>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Contacts</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_contacts); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">New (7 Days)</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($new_contacts); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-plus fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Unread</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($unread_contacts); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">High Priority</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($high_priority); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-fire fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Recent Contacts -->
                    <div class="col-lg-8 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">Recent Contact Messages</h6>
                                <a href="<?php echo ADMIN_URL; ?>/contacts.php" class="btn btn-sm btn-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_contacts)): ?>
                                <p class="text-muted text-center py-4">No contact messages yet.</p>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_contacts as $contact): ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo ADMIN_URL; ?>/contact-view.php?id=<?php echo $contact['id']; ?>" class="text-decoration-none">
                                                        <?php echo escape_html($contact['first_name'] . ' ' . $contact['last_name']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo escape_html($contact['email']); ?></td>
                                                <td><?php echo escape_html(truncate_text($contact['subject'] ?: $contact['message'], 30)); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $contact['status'] === 'new' ? 'warning' : ($contact['status'] === 'read' ? 'info' : ($contact['status'] === 'replied' ? 'success' : 'secondary')); ?>">
                                                        <?php echo ucfirst($contact['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo time_ago($contact['created_at']); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Statistics -->
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Contact Status Overview</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($status_stats)): ?>
                                <p class="text-muted text-center py-4">No data available.</p>
                                <?php else: ?>
                                <?php foreach ($status_stats as $stat): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="text-capitalize"><?php echo escape_html($stat['status']); ?></span>
                                    <span class="badge bg-<?php echo $stat['status'] === 'new' ? 'warning' : ($stat['status'] === 'read' ? 'info' : ($stat['status'] === 'replied' ? 'success' : 'secondary')); ?>">
                                        <?php echo number_format($stat['count']); ?>
                                    </span>
                                </div>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="card shadow mt-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="<?php echo ADMIN_URL; ?>/contacts.php?status=new" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-eye me-2"></i>View New Messages
                                    </a>
                                    <a href="<?php echo ADMIN_URL; ?>/contacts.php?priority=high,urgent" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-fire me-2"></i>High Priority Items
                                    </a>
                                    <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="fas fa-external-link-alt me-2"></i>View Website
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/admin.js"></script>
</body>
</html>
