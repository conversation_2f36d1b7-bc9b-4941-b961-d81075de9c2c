# Quick Setup Guide for WRANOVSKY Project

## Running the Project Locally

### Option 1: XAMPP (Recommended for Windows)

1. **Download and Install XAMPP**
   - Download from: https://www.apachefriends.org/
   - Install with Apache, MySQL, and PHP

2. **Setup Project**
   ```bash
   # Copy project files to XAMPP htdocs
   # From: C:\Users\<USER>\OneDrive\Desktop\chan\chan
   # To: C:\xampp\htdocs\wranovsky\
   ```

3. **Start Services**
   - Open XAMPP Control Panel
   - Start Apache and MySQL

4. **Create Database**
   - Open http://localhost/phpmyadmin
   - Create database: `wranovsky_cms`
   - Import: `database_schema.sql`

5. **Configure Database**
   - Edit `config/database.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'wranovsky_cms');
   define('DB_USER', 'root');
   define('DB_PASS', ''); // Usually empty for XAMPP
   ```

6. **Update Site URL**
   - Edit `config/config.php`:
   ```php
   define('SITE_URL', 'http://localhost/wranovsky');
   ```

7. **Access Website**
   - Frontend: http://localhost/wranovsky
   - Admin: http://localhost/wranovsky/admin/login.php
   - Login: admin / admin123

### Option 2: WAMP (Alternative for Windows)

1. **Download WAMP**
   - Download from: http://www.wampserver.com/
   - Install and start services

2. **Follow similar steps as XAMPP**
   - Copy files to `C:\wamp64\www\wranovsky\`
   - Access via http://localhost/wranovsky

### Option 3: Built-in PHP Server (Quick Testing)

```bash
# Navigate to project directory
cd C:\Users\<USER>\OneDrive\Desktop\chan\chan

# Start PHP built-in server
php -S localhost:8000

# Access via http://localhost:8000
```

**Note**: You'll still need MySQL running separately for this option.

## Troubleshooting

### Common Issues:

1. **Database Connection Error**
   - Check MySQL is running
   - Verify database credentials
   - Ensure database exists

2. **File Upload Issues**
   - Check `uploads/` folder permissions
   - Verify PHP upload settings

3. **Admin Login Issues**
   - Ensure database is imported correctly
   - Check default admin account exists

### PHP Requirements Check:
```php
<?php
// Create check.php in project root
phpinfo();
// Check for: PDO MySQL, mbstring, fileinfo, openssl
?>
```
