            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3"><?php echo escape_html(COMPANY_NAME); ?></h5>
                    <p class="mb-3">Discover the highest quality Bohemian crystal chandeliers. Proudly handmade in the Czech Republic, we manufacture traditional & modern chandeliers and custom lighting.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 mb-4">
                    <h6 class="mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo SITE_URL; ?>" class="text-light text-decoration-none">Home</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/about.php" class="text-light text-decoration-none">About Us</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/products.php" class="text-light text-decoration-none">Products</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/projects.php" class="text-light text-decoration-none">Projects</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/news.php" class="text-light text-decoration-none">News</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 mb-4">
                    <h6 class="mb-3">Products</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo SITE_URL; ?>/products.php?category=traditional" class="text-light text-decoration-none">Traditional Chandeliers</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/products.php?category=modern" class="text-light text-decoration-none">Modern Chandeliers</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/products.php?category=custom" class="text-light text-decoration-none">Custom Lighting</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/products.php?category=accessories" class="text-light text-decoration-none">Accessories</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 mb-4">
                    <h6 class="mb-3">Contact Info</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <?php echo escape_html(COMPANY_ADDRESS); ?>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:<?php echo escape_attr(str_replace(' ', '', COMPANY_PHONE)); ?>" class="text-light text-decoration-none"><?php echo escape_html(COMPANY_PHONE); ?></a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<?php echo escape_attr(COMPANY_EMAIL); ?>" class="text-light text-decoration-none"><?php echo escape_html(COMPANY_EMAIL); ?></a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo escape_html(COMPANY_FULL_NAME); ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="<?php echo SITE_URL; ?>/privacy.php" class="text-light text-decoration-none me-3">Privacy Policy</a>
                    <a href="<?php echo SITE_URL; ?>/terms.php" class="text-light text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scroll to Top Button -->
    <button type="button" class="btn btn-primary btn-floating btn-lg" id="btn-back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo escape_attr($js); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Custom JavaScript -->
    <script>
        // Scroll to top functionality
        let mybutton = document.getElementById("btn-back-to-top");
        
        window.onscroll = function () {
            scrollFunction();
        };
        
        function scrollFunction() {
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                mybutton.style.display = "block";
            } else {
                mybutton.style.display = "none";
            }
        }
        
        mybutton.addEventListener("click", function() {
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            let alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                let bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Form validation enhancement
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
    
    <?php if (isset($inline_js)): ?>
    <script>
        <?php echo $inline_js; ?>
    </script>
    <?php endif; ?>
</body>
</html>
