@echo off
echo ========================================
echo WRANOVSKY Project Setup for Windows
echo ========================================
echo.

echo This script will help you set up the WRANOVSKY project locally.
echo.

echo Prerequisites:
echo - XAMPP or WAMP installed
echo - MySQL running
echo - PHP 7.4+ with required extensions
echo.

pause

echo.
echo Step 1: Checking PHP installation...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install XAMPP or add PHP to your PATH
    pause
    exit /b 1
)

echo.
echo Step 2: Checking MySQL connection...
echo Please make sure MySQL is running in XAMPP/WAMP
pause

echo.
echo Step 3: Database Setup
echo.
echo Please follow these steps manually:
echo 1. Open http://localhost/phpmyadmin
echo 2. Create a new database named: wranovsky_cms
echo 3. Import the file: database_schema.sql
echo 4. Create a database user (optional but recommended)
echo.
pause

echo.
echo Step 4: Configuration
echo.
echo Please edit these files with your settings:
echo - config/database.php (database credentials)
echo - config/config.php (site URL and email settings)
echo.
echo For XAMPP default settings:
echo - DB_HOST: localhost
echo - DB_USER: root
echo - DB_PASS: (leave empty)
echo - SITE_URL: http://localhost/wranovsky
echo.
pause

echo.
echo Step 5: File Permissions
echo.
echo Making uploads directory writable...
if not exist "uploads" mkdir uploads
if not exist "uploads\contacts" mkdir uploads\contacts
if not exist "uploads\temp" mkdir uploads\temp
echo Uploads directories created.

echo.
echo Step 6: Testing
echo.
echo To test your installation:
echo 1. Copy this project to: C:\xampp\htdocs\wranovsky\
echo 2. Start Apache and MySQL in XAMPP
echo 3. Visit: http://localhost/wranovsky
echo 4. Admin panel: http://localhost/wranovsky/admin/login.php
echo 5. Default login: admin / admin123 (CHANGE IMMEDIATELY!)
echo.

echo.
echo Step 7: Multi-Language Support
echo.
echo The site now supports English and Arabic:
echo - Language switcher in navigation
echo - RTL support for Arabic
echo - Complete translations
echo - Test with: ?lang=ar or ?lang=en
echo.

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Configure database connection
echo 2. Import database schema
echo 3. Update site settings
echo 4. Change default admin password
echo 5. Test contact form
echo.
echo For detailed instructions, see:
echo - INSTALLATION.md
echo - SETUP_GUIDE.md
echo - README.md
echo.
pause
